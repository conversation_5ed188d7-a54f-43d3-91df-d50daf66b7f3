import React, { useState, useEffect } from 'react';
import { getHighScores } from '../services/firebase';
import '../styles/high-score-board.css';

const HighScoreBoard = ({ game, isVisible, onClose }) => {
  const [scores, setScores] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isVisible && game) {
      fetchHighScores();
    }
  }, [isVisible, game]);

  const fetchHighScores = async () => {
    setLoading(true);
    setError('');
    
    try {
      const result = await getHighScores(game, 10);
      if (result.success) {
        setScores(result.scores);
      } else {
        setError('Failed to load high scores');
      }
    } catch (error) {
      console.error('Error fetching high scores:', error);
      setError('Failed to load high scores');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  const getGameEmoji = (gameName) => {
    switch (gameName) {
      case 'snake': return '🐍';
      case 'brick-breaker': return '🧱';
      default: return '🎮';
    }
  };

  if (!isVisible) return null;

  return (
    <div className="high-score-overlay">
      <div className="high-score-board">
        <div className="board-header">
          <h2>{getGameEmoji(game)} High Scores - {game.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="scores-container">
          {loading && (
            <div className="loading-message">
              <div className="loading-spinner"></div>
              Loading high scores...
            </div>
          )}

          {error && (
            <div className="error-message">
              {error}
              <button onClick={fetchHighScores} className="retry-button">
                Try Again
              </button>
            </div>
          )}

          {!loading && !error && scores.length === 0 && (
            <div className="no-scores-message">
              <div className="no-scores-icon">🏆</div>
              <p>No high scores yet!</p>
              <p>Be the first to set a record!</p>
            </div>
          )}

          {!loading && !error && scores.length > 0 && (
            <div className="scores-list">
              <div className="scores-header">
                <span className="rank-header">Rank</span>
                <span className="name-header">Name</span>
                <span className="score-header">Score</span>
                <span className="date-header">Date</span>
              </div>
              
              {scores.map((score, index) => (
                <div 
                  key={score.id} 
                  className={`score-row ${index < 3 ? `rank-${index + 1}` : ''}`}
                >
                  <span className="rank">
                    {index === 0 && '🥇'}
                    {index === 1 && '🥈'}
                    {index === 2 && '🥉'}
                    {index > 2 && `#${index + 1}`}
                  </span>
                  <span className="name">{score.playerName}</span>
                  <span className="score">{score.score.toLocaleString()}</span>
                  <span className="date">{formatDate(score.timestamp)}</span>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="board-footer">
          <button className="refresh-button" onClick={fetchHighScores} disabled={loading}>
            🔄 Refresh
          </button>
          <button className="close-footer-button" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default HighScoreBoard;
