import { useState, useEffect } from 'react';
import { Browser<PERSON><PERSON>er, HashRouter, Routes, Route, Navigate } from 'react-router-dom';

// Component that detects if <PERSON><PERSON><PERSON><PERSON>out<PERSON> works and falls back to HashRouter
const SmartRouter = ({ children }) => {
  const [useBrowserRouter, setUseBrowserRouter] = useState(true);
  const [routerReady, setRouterReady] = useState(false);

  useEffect(() => {
    // Test if we're in a situation where BrowserRouter won't work
    const testBrowserRouter = () => {
      // If we're already on a hash URL, use HashRouter
      if (window.location.hash.startsWith('#/')) {
        setUseBrowserRouter(false);
        setRouterReady(true);
        return;
      }

      // If we're on a non-root path and got here directly (not through navigation)
      // it means the server routing is working
      const path = window.location.pathname;
      if (path !== '/' && !document.referrer.includes(window.location.origin)) {
        // We're on a non-root path and came from outside - server routing works
        setUseBrowserRouter(true);
        setRouterReady(true);
        return;
      }

      // For root path or internal navigation, assume BrowserRouter works
      setUseBrowserRouter(true);
      setRouterReady(true);
    };

    testBrowserRouter();
  }, []);

  // Show loading while determining router type
  if (!routerReady) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontFamily: 'Josefin Sans, Arial, sans-serif',
        background: '#f0ead6',
        color: '#0d47a1'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            border: '4px solid #f3f3f3',
            borderTop: '4px solid #0d47a1',
            borderRadius: '50%',
            width: '40px',
            height: '40px',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p>Loading The Grin Bin...</p>
        </div>
      </div>
    );
  }

  // Use the appropriate router
  const RouterComponent = useBrowserRouter ? BrowserRouter : HashRouter;
  
  return (
    <RouterComponent>
      {children}
    </RouterComponent>
  );
};

export default SmartRouter;
