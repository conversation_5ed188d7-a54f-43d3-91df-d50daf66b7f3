.high-score-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-in-out;
}

.high-score-modal {
  background: var(--card-bg);
  border: 2px solid var(--accent-color);
  border-radius: 15px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
}

.high-score-modal h2 {
  color: var(--accent-color);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.score-display {
  background: var(--bg-secondary);
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
}

.score-label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.score-value {
  display: block;
  color: var(--accent-color);
  font-size: 2rem;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.name-input-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.input-label {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 1.1rem;
}

.name-input {
  padding: 0.8rem;
  font-size: 1.5rem;
  text-align: center;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
  font-weight: bold;
  letter-spacing: 0.2em;
  transition: all 0.2s ease;
}

.name-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb), 0.2);
}

.name-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  color: #ff4444;
  font-size: 0.9rem;
  padding: 0.5rem;
  background: rgba(255, 68, 68, 0.1);
  border-radius: 5px;
  border: 1px solid rgba(255, 68, 68, 0.3);
}

.button-group {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.submit-button,
.cancel-button {
  flex: 1;
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-button {
  background: var(--accent-color);
  color: white;
}

.submit-button:hover:not(:disabled) {
  background: var(--accent-hover);
  transform: translateY(-1px);
}

.submit-button:disabled {
  background: #666;
  cursor: not-allowed;
  transform: none;
}

.cancel-button {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.cancel-button:hover:not(:disabled) {
  background: var(--border-color);
  transform: translateY(-1px);
}

.input-hint {
  margin-top: 1rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-style: italic;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .high-score-modal {
    padding: 1.5rem;
    margin: 1rem;
  }
  
  .high-score-modal h2 {
    font-size: 1.5rem;
  }
  
  .score-value {
    font-size: 1.5rem;
  }
  
  .name-input {
    font-size: 1.3rem;
  }
  
  .button-group {
    flex-direction: column;
  }
}
