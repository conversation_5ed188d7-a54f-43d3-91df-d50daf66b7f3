.high-score-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-in-out;
}

.high-score-board {
  background: var(--card-bg);
  border: 2px solid var(--accent-color);
  border-radius: 15px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
}

.board-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.board-header h2 {
  color: var(--accent-color);
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.scores-container {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  min-height: 300px;
}

.loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-secondary);
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #ff4444;
  gap: 1rem;
}

.retry-button {
  padding: 0.5rem 1rem;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.retry-button:hover {
  background: var(--accent-hover);
}

.no-scores-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-secondary);
  text-align: center;
}

.no-scores-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.scores-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.scores-header {
  display: grid;
  grid-template-columns: 60px 1fr 100px 80px;
  gap: 1rem;
  padding: 0.8rem;
  background: var(--bg-secondary);
  border-radius: 8px;
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.score-row {
  display: grid;
  grid-template-columns: 60px 1fr 100px 80px;
  gap: 1rem;
  padding: 1rem 0.8rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.score-row:hover {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

.score-row.rank-1 {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
  border-color: rgba(255, 215, 0, 0.3);
}

.score-row.rank-2 {
  background: linear-gradient(135deg, rgba(192, 192, 192, 0.1), rgba(192, 192, 192, 0.05));
  border-color: rgba(192, 192, 192, 0.3);
}

.score-row.rank-3 {
  background: linear-gradient(135deg, rgba(205, 127, 50, 0.1), rgba(205, 127, 50, 0.05));
  border-color: rgba(205, 127, 50, 0.3);
}

.rank {
  font-size: 1.2rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.name {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  font-size: 1.1rem;
  color: var(--accent-color);
  display: flex;
  align-items: center;
}

.score {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: var(--text-primary);
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.date {
  color: var(--text-secondary);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.board-footer {
  display: flex;
  justify-content: space-between;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
  gap: 1rem;
}

.refresh-button,
.close-footer-button {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-button {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.refresh-button:hover:not(:disabled) {
  background: var(--border-color);
  transform: translateY(-1px);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.close-footer-button {
  background: var(--accent-color);
  color: white;
}

.close-footer-button:hover {
  background: var(--accent-hover);
  transform: translateY(-1px);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile responsiveness */
@media (max-width: 600px) {
  .high-score-board {
    width: 95%;
    max-height: 85vh;
  }
  
  .board-header {
    padding: 1rem;
  }
  
  .board-header h2 {
    font-size: 1.2rem;
  }
  
  .scores-container {
    padding: 1rem;
  }
  
  .scores-header,
  .score-row {
    grid-template-columns: 50px 1fr 80px 60px;
    gap: 0.5rem;
    padding: 0.8rem 0.5rem;
  }
  
  .rank {
    font-size: 1rem;
  }
  
  .name {
    font-size: 1rem;
  }
  
  .score {
    font-size: 0.9rem;
  }
  
  .date {
    font-size: 0.8rem;
  }
  
  .board-footer {
    padding: 1rem;
    flex-direction: column;
  }
}
