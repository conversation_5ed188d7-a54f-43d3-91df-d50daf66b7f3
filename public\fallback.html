<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Grin Bin - Redirecting...</title>
    <style>
        body {
            font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
            background: #f0ead6;
            color: #0d47a1;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            text-align: center;
        }
        .container {
            max-width: 500px;
            padding: 2rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0d47a1;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        a {
            color: #0d47a1;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>Redirecting to The Grin Bin...</h2>
        <p>If you're not redirected automatically, <a href="/" id="homeLink">click here to go to the homepage</a>.</p>
        <p><small>This page appears when there's a routing issue. We're working to fix it!</small></p>
    </div>

    <script>
        // Try to redirect to the homepage after a short delay
        setTimeout(function() {
            window.location.href = '/';
        }, 2000);

        // Also try to detect the intended path and redirect there
        const path = window.location.pathname;
        if (path && path !== '/') {
            // Try to redirect to the intended path with hash routing as fallback
            setTimeout(function() {
                window.location.href = '/#' + path;
            }, 3000);
        }
    </script>
</body>
</html>
