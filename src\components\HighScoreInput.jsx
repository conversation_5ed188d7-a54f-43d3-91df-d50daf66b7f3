import React, { useState } from 'react';
import { submitHighScore } from '../services/firebase';
import '../styles/high-score-input.css';

const HighScoreInput = ({ score, game, onSubmit, onCancel }) => {
  const [playerName, setPlayerName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleInputChange = (e) => {
    const value = e.target.value.toUpperCase().replace(/[^A-Z]/g, '');
    if (value.length <= 3) {
      setPlayerName(value);
      setError('');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (playerName.length !== 3) {
      setError('Please enter exactly 3 letters');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const result = await submitHighScore(playerName, score, game);
      
      if (result.success) {
        onSubmit(result);
      } else {
        setError('Failed to submit score. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting high score:', error);
      setError('Failed to submit score. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && playerName.length === 3) {
      handleSubmit(e);
    } else if (e.key === 'Escape') {
      onCancel();
    }
  };

  return (
    <div className="high-score-overlay">
      <div className="high-score-modal">
        <h2>🏆 New High Score!</h2>
        <div className="score-display">
          <span className="score-label">Your Score:</span>
          <span className="score-value">{score.toLocaleString()}</span>
        </div>
        
        <form onSubmit={handleSubmit} className="name-input-form">
          <label htmlFor="playerName" className="input-label">
            Enter your initials (3 letters):
          </label>
          <input
            id="playerName"
            type="text"
            value={playerName}
            onChange={handleInputChange}
            onKeyDown={handleKeyPress}
            placeholder="ABC"
            maxLength={3}
            className="name-input"
            autoFocus
            disabled={isSubmitting}
          />
          
          {error && <div className="error-message">{error}</div>}
          
          <div className="button-group">
            <button
              type="submit"
              disabled={playerName.length !== 3 || isSubmitting}
              className="submit-button"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Score'}
            </button>
            <button
              type="button"
              onClick={onCancel}
              disabled={isSubmitting}
              className="cancel-button"
            >
              Cancel
            </button>
          </div>
        </form>
        
        <div className="input-hint">
          Use letters A-Z only. Numbers and special characters will be removed.
        </div>
      </div>
    </div>
  );
};

export default HighScoreInput;
