import React, { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { ThemeContext } from '../context/ThemeContext';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SnakeGame from '../components/SnakeGame';
import Footer from '../components/Footer';
import CookieConsent from '../components/CookieConsent';
import { testFirebaseConnection, submitHighScore } from '../services/firebase';
import logoLight from '../assets/text-only-modern.svg';
import logoDark from '../assets/text-only-modern-dark.svg';
import '../styles/app.css';
import '../styles/game-page.css';

const SnakePage = () => {
  const { darkMode, toggleTheme } = useContext(ThemeContext);
  const navigate = useNavigate();

  const handleNavigate = (page) => {
    navigate(`/${page}`);
  };

  // Test Firebase connection
  const testFirebase = async () => {
    console.log('Testing Firebase connection...');
    const result = await testFirebaseConnection();
    console.log('Test result:', result);

    if (result.success) {
      // Try submitting a test score
      const testScore = await submitHighScore('TST', 100, 'snake');
      console.log('Test score submission:', testScore);
      alert('Firebase test successful! Check console for details.');
    } else {
      alert('Firebase test failed: ' + result.error);
    }
  };

  return (
    <div className={`app ${darkMode ? 'dark-mode' : 'light-mode'}`}>
      <div className="theme-toggle">
        <button onClick={toggleTheme} className="theme-button">
          {darkMode ? <Brightness7Icon /> : <Brightness4Icon />}
        </button>
      </div>

      <div className="logo-container">
        <img
          src={darkMode ? logoDark : logoLight}
          alt="The Grin Bin"
          className="main-logo"
          onClick={() => navigate('/')}
          style={{ cursor: 'pointer' }}
        />
      </div>

      <div className="game-page-container">
        <div className="game-header">
          <button
            className="back-button"
            onClick={() => navigate('/classic-games')}
          >
            <ArrowBackIcon /> Back to Games
          </button>
          <h1 className="game-title">🐍 Snake</h1>
          <p className="game-description">
            Guide the snake to eat food and grow longer without hitting the walls or yourself!
            Use arrow keys or WASD on desktop, or use the on-screen controls and swipe gestures on mobile.
            The classic arcade game that never gets old - how long can you make your snake?
          </p>
        </div>

        {/* High Scores Button */}
        <div style={{
          marginBottom: '20px',
          display: 'flex',
          justifyContent: 'center',
          gap: '10px'
        }}>
          <button
            style={{
              padding: '10px 20px',
              fontSize: '16px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#45a049';
              e.target.style.transform = 'translateY(-1px)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = '#4CAF50';
              e.target.style.transform = 'translateY(0)';
            }}
            onClick={() => {
              const event = new CustomEvent('showHighScores', { detail: { game: 'snake' } });
              window.dispatchEvent(event);
            }}
          >
            🏆 View High Scores
          </button>

          {/* Temporary Firebase Test Button */}
          <button
            style={{
              padding: '10px 20px',
              fontSize: '16px',
              backgroundColor: '#FF9800',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onClick={testFirebase}
          >
            🔧 Test Firebase
          </button>
        </div>

        <div className="game-container">
          <SnakeGame />
        </div>

        <div className="game-instructions">
          <h3>How to Play:</h3>
          <ul>
            <li>🎮🖱️ Use arrow keys, WASD, or on-screen controls to move</li>
            <li>📱 Swipe on the game area for mobile control</li>
            <li>🍎 Eat red food to grow your snake and score points</li>
            <li>⚠️ Don't hit walls or your own body!</li>
            <li>🏆 Try to beat your high score and grow as long as possible</li>
            <li>💾 Your best score is automatically saved</li>
          </ul>
        </div>
      </div>

      <Footer onNavigate={handleNavigate} />
      <CookieConsent />
    </div>
  );
};

export default SnakePage;
