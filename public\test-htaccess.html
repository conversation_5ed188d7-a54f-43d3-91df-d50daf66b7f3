<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test .htaccess - The Grin Bin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f0ead6;
            color: #0d47a1;
        }
        .test-box {
            background: white;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            border-left: 4px solid #0d47a1;
        }
        .success { border-left-color: #4caf50; }
        .error { border-left-color: #f44336; }
        .warning { border-left-color: #ff9800; }
        code {
            background: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        button {
            background: #0d47a1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
    </style>
</head>
<body>
    <h1>The Grin Bin - Server Configuration Test</h1>
    
    <div class="test-box">
        <h3>🔧 .htaccess Test Page</h3>
        <p>This page helps diagnose routing issues with your React SPA.</p>
        <p><strong>Current URL:</strong> <code id="currentUrl"></code></p>
        <p><strong>Referrer:</strong> <code id="referrer"></code></p>
    </div>

    <div class="test-box warning">
        <h3>⚠️ If you can see this page:</h3>
        <p>This means the server is NOT properly redirecting routes to your React app. The .htaccess file may not be working correctly.</p>
    </div>

    <div class="test-box">
        <h3>🧪 Test Links</h3>
        <p>Try these links to test routing:</p>
        <button onclick="testRoute('/snake')">Test /snake</button>
        <button onclick="testRoute('/brick-breaker')">Test /brick-breaker</button>
        <button onclick="testRoute('/wordplay')">Test /wordplay</button>
        <button onclick="testRoute('/about')">Test /about</button>
        <br><br>
        <button onclick="window.location.href='/'">Go to Homepage</button>
    </div>

    <div class="test-box">
        <h3>🔍 Troubleshooting Steps</h3>
        <ol>
            <li>Check if .htaccess file exists in the web root</li>
            <li>Verify mod_rewrite is enabled on the server</li>
            <li>Check if AllowOverride is set to All in Apache config</li>
            <li>Look at server error logs for any .htaccess errors</li>
            <li>Contact your hosting provider if issues persist</li>
        </ol>
    </div>

    <div class="test-box">
        <h3>📋 Server Information</h3>
        <p><strong>User Agent:</strong> <code id="userAgent"></code></p>
        <p><strong>Timestamp:</strong> <code id="timestamp"></code></p>
    </div>

    <script>
        // Display current information
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('referrer').textContent = document.referrer || 'None';
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('timestamp').textContent = new Date().toISOString();

        function testRoute(path) {
            window.location.href = path;
        }

        // Auto-redirect to homepage after 10 seconds
        setTimeout(function() {
            if (confirm('Auto-redirect to homepage in 3 seconds. Cancel to stay on this test page.')) {
                window.location.href = '/';
            }
        }, 7000);
    </script>
</body>
</html>
