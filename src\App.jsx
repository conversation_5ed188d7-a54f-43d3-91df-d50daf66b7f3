import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfService from './pages/TermsOfService';
import About from './pages/About';
import CommunityGuidelines from './pages/CommunityGuidelines';
import DMCAPolicy from './pages/DMCAPolicy';
import Contact from './pages/Contact';
import LandingPage from './pages/LandingPage';
import WordPlay from './pages/WordPlay';
import ClassicGames from './pages/ClassicGames';
import BrickBreakerPage from './pages/BrickBreakerPage';
import SnakePage from './pages/SnakePage';

import './styles/app.css';

// Main App component with routing
function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<LandingPage />} />
        <Route path="/wordplay" element={<WordPlay />} />
        <Route path="/classic-games" element={<ClassicGames />} />
        <Route path="/brick-breaker" element={<BrickBreakerPage />} />
        <Route path="/snake" element={<SnakePage />} />
        <Route path="/privacy" element={<PrivacyPolicy />} />
        <Route path="/terms" element={<TermsOfService />} />
        <Route path="/about" element={<About />} />
        <Route path="/guidelines" element={<CommunityGuidelines />} />
        <Route path="/dmca" element={<DMCAPolicy />} />
        <Route path="/contact" element={<Contact />} />
        {/* Catch-all route for any unmatched paths - redirect to home */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
}

export default App;
